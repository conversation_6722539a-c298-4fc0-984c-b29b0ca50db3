import { suite, test } from "mocha-typescript";
import { expect, should, use } from "chai";
import * as chaiAsPromised from "chai-as-promised";
import { truncate } from "../entities/helper";
import { factory } from "factory-girl";
import { FACTORY } from "../factories/common";
import { EntityGame } from "../../skywind/entities/game";
import { findPlayerLimits } from "../../skywind/services/limits";
import { SinonFakeTimers, SinonStub, stub, useFakeTimers } from "sinon";
import * as gameLimitsCurrenciesCache from "../../skywind/cache/gameLimitsCurrencies";
import * as EntityJurisdictionCache from "../../skywind/cache/entityJurisdiction";

should();
use(chaiAsPromised);

@suite()
class MaxTotalBetFiltersSpec {
    public static entityGame: EntityGame;
    public clock: SinonFakeTimers;
    public static gameLimitsCurrenciesCacheStub: SinonStub;

    public static async before() {
        await truncate();
        await gameLimitsCurrenciesCache.reset();
        MaxTotalBetFiltersSpec.gameLimitsCurrenciesCacheStub = stub(gameLimitsCurrenciesCache, "getGameLimitsCurrency");
        MaxTotalBetFiltersSpec.gameLimitsCurrenciesCacheStub.resolves({});
    }

    public static after() {
        MaxTotalBetFiltersSpec.gameLimitsCurrenciesCacheStub.restore();
    }

    public before() {
        this.clock = useFakeTimers();
        this.clock.setSystemTime(0);
    }

    public after() {
        this.clock.restore();
    }

    @test("should limit max total stake by operator when feature limiting is disabled")
    public async testMaxTotalStakeIsLimitedByOperatorAndFeatureIsNotLimited() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 25,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: false } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 100,
            stakeAll: [1, 2],
            stakeDef: 2,
            stakeMax: 2,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test("should limit max total stake and features by operator when feature limiting is enabled")
    public async testMaxTotalStakeIsLimitedByOperatorAndFeatureIsLimitedByOperator() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 25,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 25,
            stakeAll: [1, 2],
            stakeDef: 2,
            stakeMax: 2,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test("should limit max total stake and features by regulation when no operator limits exist")
    public async testMaxTotalStakeAndFeaturesIsLimitedByRegulation() {
        EntityJurisdictionCache.reset();
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 25
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            undefined,
            { limitFeaturesToMaxTotalStake: false } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 25,
            stakeAll: [1, 2],
            stakeDef: 2,
            stakeMax: 2,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test("should use regulation limit when operator limit is higher than regulation limit")
    public async testMaxTotalStakeIsLimitedByOperatorAndRegulationWhereOperatorMTBMoreThanRegulation() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 45,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 30
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.get("name"),
            { limitFeaturesToMaxTotalStake: false } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 30,
            stakeAll: [1, 2, 3],
            stakeDef: 2,
            stakeMax: 3,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test("should use operator limit when regulation limit is higher than operator limit")
    public async testMaxTotalStakeIsLimitedByOperatorAndRegulationWhereRegulationMTBMoreThanOperator() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 25,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 45
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.get("name"),
            { limitFeaturesToMaxTotalStake: false } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 45,
            stakeAll: [1, 2],
            stakeDef: 2,
            stakeMax: 2,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test("should use regulation limit for features when operator limit is higher than regulation limit")
    public async testMaxTotalStakeAndFeatureIsLimitedByRegulationAndOperatorWhereOperatorMTBMoreThanRegulation() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 45,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 30
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.get("name"),
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 30,
            stakeAll: [1, 2, 3],
            stakeDef: 2,
            stakeMax: 3,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test("should use operator limit for features when regulation limit is higher than operator limit")
    public async testMaxTotalStakeAndFeatureIsLimitedByRegulationAndOperatorWhereRegulationMTBMoreThanOperator() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 30,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 45
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.get("name"),
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 30,
            stakeAll: [1, 2, 3],
            stakeDef: 2,
            stakeMax: 3,
            stakeMin: 1,
            winMax: 500000
        });
    }

}
