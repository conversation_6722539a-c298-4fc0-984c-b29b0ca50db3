import { Models } from "../../skywind/models/models";
import { complexStructure, createComplexStructure, truncate, flushAll } from "./helper";
import * as EntityService from "../../skywind/services/entity";
import * as Errors from "../../skywind/errors";
import { SamePasswordAndUsernameError } from "../../skywind/errors";
import { DetailedUserInfo, LoginInfo, User, UserInfo, UserType } from "../../skywind/entities/user";
import { BaseEntity, Entity } from "../../skywind/entities/entity";
import { stub, SinonStub } from "sinon";
import * as EmailUtils from "../../skywind/utils/emails";
import { createResetToken } from "../../skywind/services/security";
import { Op, ValidationError } from "sequelize";
import * as redis from "../../skywind/storage/redis";
import EntitySettingsService from "../../skywind/services/settings";
import config from "../../skywind/config";
import { sleep } from "../helper";
import * as SmsUtils from "../../skywind/utils/sms";
import { TIME_CONSTS, TWO_FA_TYPE } from "../../skywind/utils/common";
import getUserService, {
    changeEmail,
    confirmEmail,
    forceChangeEmail,
    requestChangeEmail,
    search,
    UserService
} from "../../skywind/services/user/user";
import getEntityFactory, { CreateData } from "../../skywind/services/entityFactory";
import { getUserAuthService } from "../../skywind/services/user/userAuth";
import { getUserPasswordService } from "../../skywind/services/user/userPassword";

import { FACTORY } from "../factories/common";
import { factory } from "factory-girl";
import { getEmailService } from "../../skywind/services/email";
import * as PermissionService from "../../skywind/services/permission";
import { getRoleModel } from "../../skywind/models/role";
import { RoleImpl } from "../../skywind/services/role";
import { PagingHelper } from "../../skywind/utils/paginghelper";
import { randomUUID } from "node:crypto";

const chai = require("chai");
chai.use(require("chai-shallow-deep-equal"));
const expect = chai.expect;
chai.should();
chai.use(require("chai-as-promised"));
chai.use(require("chai-datetime"));

const authenticator = require("authenticator");

describe("Entities", () => {

    describe("User", () => {
        let masterUserService: UserService;

        const LOGIN_ATTEMPTS = config.settingsDefaults.userLoginAttempts;
        const CHANGE_PASSWORD_ATTEMPTS = config.settingsDefaults.userChangePasswordAttempts;
        const IDLE_TIMEOUT = config.settingsDefaults.userIdleTimeout;
        const CHECK_DOMAIN = config.checkEntityDomain;
        const RESET_BLOCKING_TIMEOUT = config.settingsDefaults.userResetBlockingTimeout;

        before(async () => {

            await truncate();
            const master = await createComplexStructure();
            masterUserService = getUserService(master);
            config.settingsDefaults.userLoginAttempts = 1;
            config.settingsDefaults.userIdleTimeout = 1;
            config.settingsDefaults.userResetBlockingTimeout = 1;
            config.settingsDefaults.userChangePasswordAttempts = 1;
            config.checkEntityDomain = true;
        });

        after(async () => {
            config.settingsDefaults.userLoginAttempts = LOGIN_ATTEMPTS;
            config.settingsDefaults.userIdleTimeout = IDLE_TIMEOUT;
            config.checkEntityDomain = CHECK_DOMAIN;
            config.settingsDefaults.userChangePasswordAttempts = CHANGE_PASSWORD_ATTEMPTS;
            config.settingsDefaults.userResetBlockingTimeout = RESET_BLOCKING_TIMEOUT;
        });

        it("Validates user", async () => {
            const data = {
                username: "!@#$%^",
                password: "pwd",
                email: "wrong",
            };
            await masterUserService.create(data).should.be.eventually.rejectedWith(ValidationError);
        });

        it("Create master user", async () => {
            const data = {
                username: "MASTER",
                password: "password",
                email: "<EMAIL>",
            };
            const userInfo: UserInfo = await masterUserService.create(data);

            const expected = {
                username: "MASTER",
                status: "normal",
            };
            expect(userInfo).deep.equal(expected);
        });

        it("Try to create existing user - negative", async () => {
            await masterUserService.create({
                username: "NotQnique",
                password: "password",
                email: "<EMAIL>",
            });
            await masterUserService.create({
                username: "NotQnique",
                password: "password",
                email: "<EMAIL>",
            }).should.eventually.rejectedWith(Errors.UserAlreadyExistError);
        });

        it("Create entity user", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            const userInfo: UserInfo = await service.create({
                username: "Arensnuphis",
                password: "password",
                email: "<EMAIL>",
            });

            const expected = {
                "username": "Arensnuphis",
                "status": "normal",
            };
            expect(userInfo).deep.equal(expected);
        });

        it("Create user with same username", async () => {
            const entityOne: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const serviceOne = getUserService(entityOne);
            const userInfoOne: UserInfo = await serviceOne.create({
                username: "NeiTituaabine",
                password: "password",
                email: "<EMAIL>",
            });

            const entityTwo: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent2.key });
            const serviceTwo = getUserService(entityTwo);
            const userInfoTwo: UserInfo = await serviceTwo.create({
                username: "NeiTituaabine",
                password: "password",
                email: "<EMAIL>",
            });
            const expected = {
                "username": "NeiTituaabine",
                "status": "normal",
            };
            expect(userInfoOne).deep.equal(expected);
            expect(userInfoTwo).deep.equal(expected);
        });

        it("Create user with same username and password", async () => {
            const entityOne: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const serviceOne = getUserService(entityOne);
            await serviceOne.create({
                username: "NeiTituaabine1",
                password: "NeiTituaabine1",
                email: "<EMAIL>",
            }).should.eventually.rejectedWith(SamePasswordAndUsernameError);
        });

        it("Create entity with default 'operator_api' userType and change it to 'bo' successfully",
            async () => {
                const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
                const service = getUserService(entity);
                const userName = "userForUserTypeCheck";

                await service.create({
                    username: userName,
                    password: "password",
                    email: `${userName}@someone.com`,
                });

                const createdUser = await service.findOne(userName);
                expect(createdUser.userType).eq(UserType.OPERATOR_API);
                expect(createdUser.toDetailedInfo().userType).eq(UserType.OPERATOR_API);
                expect((await createdUser.toProfileInfo(entity)).userType).eq(UserType.OPERATOR_API);

                await service.update(userName, {
                    userType: UserType.BO
                });

                let updatedUser = await service.findOne(userName);
                expect(updatedUser.userType).eq(UserType.BO);
                expect(updatedUser.toDetailedInfo().userType).eq(UserType.BO);
                expect((await updatedUser.toProfileInfo(entity)).userType).eq(UserType.BO);

                await service.update(userName, {
                    userType: UserType.STUDIO
                });

                updatedUser = await service.findOne(userName);
                expect(updatedUser.userType).eq(UserType.STUDIO);
                expect(updatedUser.toDetailedInfo().userType).eq(UserType.STUDIO);
                expect((await updatedUser.toProfileInfo(entity)).userType).eq(UserType.STUDIO);
            });

        it("Check if hierarchy is suspended", async () => {
            const tle2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2.key });
            await EntityService.suspend(tle2, { name: "ENT3" });

            const tle2ent3: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2ent3.key });

            const ent3UserService = getUserService(tle2ent3);
            await ent3UserService.create({
                username: "Tuvaluan",
                password: "password",
                email: "<EMAIL>",
            }).should.eventually.rejectedWith(Errors.ParentSuspendedError);
        });

        it("Change user password", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "ENT1-USERohOne",
                password: "password",
                email: "<EMAIL>",
            });
            const userInfo: UserInfo = await getUserPasswordService(entity).changePassword("ENT1-USERohOne", {
                password: "password",
                newPassword: "newPassword1",
            });
            const expected = {
                "username": "ENT1-USERohOne",
                "status": "normal",
            };
            expect(userInfo).deep.equal(expected);
        });

        it("Change user password with the same username and password", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "ENT1-USERohOne1",
                password: "password",
                email: "<EMAIL>",
            });
            await getUserPasswordService(entity).changePassword("ENT1-USERohOne1", {
                password: "password",
                newPassword: "ENT1-USERohOne1",
            }).should.eventually.rejectedWith(SamePasswordAndUsernameError);
        });

        it("Change user password - incorrect - negative", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "Tatenen",
                password: "password",
                email: "<EMAIL>",
            });
            await getUserPasswordService(entity).changePassword("Tatenen", {
                password: "password-wrong",
                newPassword: "newPassword1",
            }).should.be.eventually.rejectedWith(Errors.PasswordIncorrect);
        });

        it("Change user password - not valid - negative", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "Validatus",
                password: "password",
                email: "<EMAIL>",
            });
            await getUserPasswordService(entity).changePassword("Validatus", {
                password: "password",
                newPassword: "newPassword",
            }).should.be.eventually.rejectedWith(Errors.ValidationPasswordError);
        });

        it("Change user password - not valid cause contains spaces - negative", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            const userName = "CheckSpacesUser";

            await service.create({
                username: userName,
                password: "password",
                email: `${userName}@someone.com`
            });
            await getUserPasswordService(entity).changePassword(userName, {
                password: "password",
                newPassword: "new Password1!",
            }).should.be.eventually.rejectedWith(Errors.ValidationPasswordError);
        });

        it("Change user password - not valid cause contains Chinese characters - negative", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            const userName = "CheckSpacesUser1";

            await service.create({
                username: userName,
                password: "password",
                email: `${userName}@someone.com`
            });
            await getUserPasswordService(entity).changePassword(userName, {
                password: "password",
                newPassword: "屁股1233SGSDGSgsfgsgd",
            }).should.be.eventually.rejectedWith(Errors.ValidationPasswordError);
        });

        it("Change user password - not valid cause contains Cyrillic characters - negative", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            const userName = "CheckSpacesUser2";

            await service.create({
                username: userName,
                password: "password",
                email: `${userName}@someone.com`
            });
            await getUserPasswordService(entity).changePassword(userName, {
                password: "password",
                newPassword: "1233SАБВGSDGSgsfgsgd",
            }).should.be.eventually.rejectedWith(Errors.ValidationPasswordError);
        });

        it("Change user password - check if hierarchy is suspended - negative", async () => {
            const tle2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2.key });
            await EntityService.restore(tle2, { name: "ENT3" });
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2ent3.key });
            const service = getUserService(entity);
            await service.create({
                username: "Ganesha",
                password: "password",
                email: "<EMAIL>",
            });

            entity.status = "suspended";
            await getUserPasswordService(entity).changePassword("Ganesha", {
                password: "password",
                newPassword: "newpassword",
            }).should.be.eventually.rejectedWith(Errors.ParentSuspendedError);
        });

        it("Change user password - check if user is suspended - negative", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "Hanuman",
                password: "password",
                email: "<EMAIL>",
            });
            await service.suspend("Hanuman");
            await getUserPasswordService(entity).changePassword("Hanuman", {
                password: "password",
                newPassword: "newPassword1",
            }).should.be.eventually.rejectedWith(Errors.ParentSuspendedError);
        });

        it("Change user password with not exist user - negative", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            await getUserPasswordService(entity).changePassword("Sunnata", {
                password: "password",
                newPassword: "newPassword1",
            }).should.be.eventually.rejectedWith(Errors.UserNotExist);
        });

        it("Suspend user", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            let userInfo: UserInfo = await service.create({
                username: "Indra",
                password: "password",
                email: "<EMAIL>",
            });

            const expected = {
                "username": "Indra",
                "status": "normal",
            };
            expect(userInfo).deep.equal(expected);
            userInfo = await service.suspend("Indra");
            expected.status = "suspended";
            expect(userInfo).deep.equal(expected);
        });

        it("Restore user", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            let userInfo: UserInfo = await service.create({
                username: "Qebehsenuef",
                password: "password",
                email: "<EMAIL>",
            });

            const expected = {
                "username": "Qebehsenuef",
                "status": "normal",
            };
            expect(userInfo).deep.equal(expected);

            userInfo = await service.suspend("Qebehsenuef");
            expected.status = "suspended";
            expect(userInfo).deep.equal(expected);

            userInfo = await service.restore("Qebehsenuef");
            expected.status = "normal";
            expect(userInfo).deep.equal(expected);
        });

        it("Suspend user - check if hierarchy is suspended - negative", async () => {
            const tle2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2.key });
            await EntityService.restore(tle2, { name: "ENT3" });
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2ent3.key });
            const service = getUserService(entity);
            await service.create({
                username: "Ta-Bitjet",
                password: "password",
                email: "<EMAIL>",
            });

            entity.status = "suspended";
            await service.suspend("Ta-Bitjet")
                .should.be.eventually.rejectedWith(Errors.ParentSuspendedError);
        });

        it("Restore user - check if hierarchy is suspended - negative", async () => {
            const tle2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2.key });
            await EntityService.restore(tle2, { name: "ENT3" });
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2ent3.key });
            const service = getUserService(entity);
            await service.create({
                username: "Bastet",
                password: "password",
                email: "<EMAIL>",
            });

            entity.status = "suspended";
            await service.restore("Bastet")
                .should.be.eventually.rejectedWith(Errors.ParentSuspendedError);
        });

        it("Login", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "Shesmetet",
                password: "password",
                email: "<EMAIL>",
            });

            const loginInfo: LoginInfo = await getUserAuthService(entity).login(
                { username: "Shesmetet", password: "password" });
            const expected = {
                "username": "Shesmetet",
            };
            expect(loginInfo)
                .contain(expected)
                .to.have.any.keys(["accessToken"]);
        });

        it("Login with not exist user - negative", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            await getUserAuthService(entity).login(
                { username: "Faunus", password: "password" })
                .should.be.eventually.rejectedWith(Errors.GenericUserLoginError);
        });

        it("Login with other password - negative", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "Pomona",
                password: "password",
                email: "<EMAIL>",
            });
            await getUserAuthService(entity).login({ username: "Pomona", password: "password-fake" })
                .should.be.eventually.rejectedWith(Errors.GenericUserLoginError);
        });

        it("Login with wrong password several times", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "Blockada",
                password: "password",
                email: "<EMAIL>",
            });

            await getUserAuthService(entity).login({ username: "Blockada", password: "password-fake" })
                .should.be.eventually.rejectedWith(Errors.GenericUserLoginError);

            await getUserAuthService(entity).login({ username: "Blockada", password: "password" })
                .should.be.eventually.rejectedWith(Errors.UserAuthenticationBlocked);
        });

        it("Login with wrong password several times under Master entity", async () => {
            const password: string = "password";
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.masterKey });
            const user = await factory.create(FACTORY.USER, {}, { password });

            await getUserAuthService(entity).login({ username: user.username, password: "password-fake" })
                .should.be.eventually.rejectedWith(Errors.GenericUserLoginError);
            await getUserAuthService(entity).login({ username: user.username, password: "password-fake" })
                .should.be.eventually.rejectedWith(Errors.GenericUserLoginError);

            const loginInfo: LoginInfo = await getUserAuthService(entity).login({ username: user.username, password });
            const expected = {
                "username": loginInfo.username,
            };
            expect(loginInfo)
                .contain(expected)
                .to.have.any.keys(["accessToken"]);
        });

        it("Get blocking info after blocked login", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            const userInfo = {
                username: "Blockada-profile",
                password: "password",
                email: "<EMAIL>",
            };
            await service.create(userInfo);

            await getUserAuthService(entity).login({ username: userInfo.username, password: "password-fake" })
                .should.be.eventually.rejectedWith(Errors.GenericUserLoginError);
            await getUserAuthService(entity).login({ username: userInfo.username, password: "password-fake" })
                .should.be.eventually.rejectedWith(Errors.UserAuthenticationBlocked);

            const userProfile = await (await service.findOne(userInfo.username)).toProfileInfo(entity);
            expect(userProfile.blocking.changePasswordTillDate).to.be.null;
        });

        it("Request to unlock user login", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "blockada1",
                password: "password",
                email: "<EMAIL>",
            });
            await getUserAuthService(entity).unlockLogin("blockada1")
                .should.eventually.rejectedWith(Errors.ClearBlockedAuthenticationFails);
            await getUserAuthService(entity).login({ username: "blockada1", password: "password-fake" })
                .should.be.eventually.rejectedWith(Errors.GenericUserLoginError);
            await sleep(100);

            await getUserAuthService(entity).login({ username: "blockada1", password: "password" })
                .should.be.eventually.rejectedWith(Errors.UserAuthenticationBlocked);

            const unlockResult = await getUserAuthService(entity).unlockLogin("blockada1");
            expect(unlockResult).equal(undefined);

            const loginInfo: LoginInfo = await getUserAuthService(entity).login(
                { username: "blockada1", password: "password" });
            const expected = {
                "username": "blockada1",
            };
            expect(loginInfo)
                .contain(expected)
                .to.have.any.keys(["accessToken"]);
        });

        it("Request to unlock user changing password", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "blockada0",
                password: "password",
                email: "<EMAIL>",
            });
            await getUserPasswordService(entity).unlockResetPassword("blockada0")
                .should.eventually.rejectedWith(Errors.ClearBlockedChangingPasswordFails);
            await getUserPasswordService(entity).changePassword(
                "blockada0",
                { password: "password-fake", newPassword: "123456qwerty" }
            ).should.be.eventually.rejectedWith(Errors.PasswordIncorrect);

            await getUserPasswordService(entity).changePassword(
                "blockada0",
                { password: "password", newPassword: "123456qwertY!" }
            ).should.be.eventually.rejectedWith(Errors.ChangePasswordBlocked);

            const unlockResult = await getUserPasswordService(entity).unlockResetPassword("blockada0");
            expect(unlockResult).equal(undefined);

            const userInfo: UserInfo = await getUserPasswordService(entity).changePassword(
                "blockada0",
                { password: "password", newPassword: "123456qwertY!" }
            );
            const expected = {
                username: "blockada0",
                status: "normal"
            };
            expect(userInfo)
                .contain(expected);
        });

        it("Get blocking info after blocked change password", async () => {
            const now = new Date();
            now.setUTCSeconds(now.getSeconds() - 1);
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            const userInfo = {
                username: "Blockada-password",
                password: "password",
                email: "<EMAIL>",
            };
            await service.create(userInfo);

            await getUserPasswordService(entity).changePassword(
                userInfo.username,
                { password: "password-fake", newPassword: "123456qwerty" }
            ).should.be.eventually.rejectedWith(Errors.PasswordIncorrect);

            await getUserPasswordService(entity).changePassword(
                userInfo.username,
                { password: "password", newPassword: "123456qwertY!" }
            ).should.be.eventually.rejectedWith(Errors.ChangePasswordBlocked);

            const userProfile = await (await service.findOne(userInfo.username)).toProfileInfo(entity);

            expect(userProfile.blocking.changePasswordTillDate).to.afterTime(now);
        });

        it("Change email simultaneously", async () => {
            const keyEntity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(keyEntity);
            const data = {
                username: "Mercury",
                password: "password",
                email: "<EMAIL>",
            };
            await service.create(data);

            const userV1 = await service.findOne(data.username) as User;
            const userV2 = await service.findOne(data.username) as User;

            userV1.email = "<EMAIL>";
            userV2.email = "<EMAIL>";

            await userV1.save();
            await userV2.save().should.be.eventually.rejectedWith(Errors.OptimisticLockException);
        });

        it("Update user username", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "Jupiter",
                password: "password",
                email: "<EMAIL>",
            });

            const updateData = {
                username: "Zeus",
            };

            const userInfo = await service.update("Jupiter", updateData);

            const expected = {
                username: "Zeus",
                status: "normal",
            };

            expect(userInfo).contain(expected);
        });

        it("Update user first name", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "Apollo",
                firstName: "Feb",
                lastName: "Zeusufamilia",
                password: "password",
                email: "<EMAIL>",
            });

            const updateData = {
                firstName: "Loxy",
            };

            await service.update("Apollo", updateData);
            const user = await service.findOne("Apollo");

            const expected = {
                firstName: "Loxy",
            };

            expect(user).contain(expected);
        });

        it("Update user last name", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "CaterinaMedici",
                firstName: "Caterina Maria Romola di Lorenzo",
                lastName: "Medici",
                password: "password",
                email: "<EMAIL>",
            });

            const updateData = {
                lastName: "Valois",
            };

            await service.update("CaterinaMedici", updateData);
            const user = await service.findOne("CaterinaMedici");

            const expected = {
                lastName: "Valois",
            };

            expect(user).contain(expected);
        });

        it("Update user custom data", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "CaterinaMedici234",
                firstName: "Caterina Maria",
                lastName: "Medici53",
                password: "welcome123",
                email: "<EMAIL>",
            });

            const updateData = {
                customData: { test: "214" },
            };

            await service.update("CaterinaMedici234", updateData);
            const user = await service.findOne("CaterinaMedici234");

            const expected = {
                test: "214",
            };

            expect(user.customData).contain(expected);
        });

        it("Update user of suspended entity - negative", async () => {
            const tle2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2.key });
            await EntityService.restore(tle2, { name: "ENT3" });
            const entity: Entity = await EntityService.findOne({ key: complexStructure.tle2ent3.key }) as Entity;
            const service = getUserService(entity);
            await service.create({
                username: "Trophonius",
                password: "password",
                email: "<EMAIL>",
            });

            entity.status = "suspended";
            await entity.save();

            const updateData = {
                username: "Ares-Fake",
            };

            await service.update("Trophonius", updateData)
                .should.be.eventually.rejectedWith(Errors.ParentSuspendedError);
        });

        it("Update suspended user successfully", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "USER1",
                password: "password",
                email: "<EMAIL>",
            });

            await service.suspend("USER1");

            const updateData = {
                username: "USER2",
            };

            await service.update("USER1", updateData)
                .should.eventually.deep.equal({
                    "status": "suspended",
                    "username": "USER2"
                });
        });

        it("Update non-existing user - negative", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "TorquatoTasso",
                password: "password",
                email: "<EMAIL>",
            });

            const updateData = {
                username: "TorquatoSalvatore",
            };

            await service.update("Torquato", updateData)
                .should.be.eventually.rejectedWith(Errors.UserNotExist);
        });

        it("Update user, set already existing username - negative", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const service = getUserService(entity);
            await service.create({
                username: "Osiris",
                password: "password",
                email: "<EMAIL>",
            });

            await service.create({
                username: "Set",
                password: "password",
                email: "<EMAIL>",
            });

            const updateData = {
                username: "Set",
            };

            await service.update("Osiris", updateData)
                .should.be.eventually.rejectedWith(Errors.UserAlreadyExistError);
        });

        describe("Password recovery", () => {
            let entity;
            let entityUserService: UserService;
            let emailTestData = {} as any;
            let emailStub: SinonStub;

            before(async () => {
                entity = await EntityService.findOne({ key: complexStructure.masterKey });
                entityUserService = getUserService(entity);
                await entityUserService.create({
                    username: "USERwithPASSWORD",
                    password: "password",
                    email: "<EMAIL>",
                });
                await (new EntitySettingsService(entity)).patch({
                    emailTemplates: {
                        passwordRecovery: {
                            from: "Test <<EMAIL>>",
                            subject: "Test Password Reset",
                            html: "<a href=\"{{resetLink}}\">{{resetLink}}</a>",
                        },
                        changeEmail: {
                            from: "Skywind <<EMAIL>>",
                            subject: "Skywind Change Email Confirmation",
                            html: "<a href=\"{{resetLink}}\">{{resetLink}}</a>",
                        },
                    }
                });
                emailStub = stub(getEmailService(), "sendEmail").callsFake(([email], data, options) => {
                    emailTestData = {
                        email,
                        options,
                        data
                    };
                });
            });

            after(async () => {
                emailStub.restore();
            });

            afterEach(async () => {
                emailStub.reset();
                await flushAll();
            });

            it("Requests password reset", async () => {
                const domain = "https://bo.domain.com";
                await getUserPasswordService(entity).requestPasswordReset({
                    secretKey: complexStructure.masterKey,
                    identifier: "USERwithPASSWORD",
                    domain
                });

                expect(emailTestData.email).to.be.equal("<EMAIL>");
                // tslint:disable-next-line:no-unused-expression
                expect(emailTestData.options).to.exist;
                expect(emailTestData.options.domain).to.be.equal(domain);
            });

            it("Fails to request password reset if data are missing", async () => {
                await getUserPasswordService(entity).requestPasswordReset({
                    secretKey: null,
                    identifier: null,
                    domain: "https://bo.domain.com"
                }).should.eventually.rejectedWith(Errors.ValidationError);
            });

            it("Not fails to request password reset for unknown entity", async () => {
                await getUserPasswordService(entity).requestPasswordReset({
                    secretKey: "unknown",
                    identifier: "USERwithPASSWORD",
                    domain: "https://bo.domain.com"
                }).should.eventually.deep.equal({});
            });

            it("Not fails to request password reset for unknown user", async () => {
                await getUserPasswordService(entity).requestPasswordReset({
                    secretKey: complexStructure.masterKey,
                    identifier: "unknown",
                    domain: "https://bo.domain.com"
                }).should.eventually.deep.equal({});
            });

            it("Fails to request password reset if user on suspended", async () => {
                await entityUserService.suspend("USERwithPASSWORD");
                await getUserPasswordService(entity).requestPasswordReset({
                    secretKey: complexStructure.masterKey,
                    identifier: "USERwithPASSWORD",
                    domain: "https://bo.domain.com"
                }).should.eventually.deep.equal({});
            });

            it("Resets password", async () => {
                await entityUserService.restore("USERwithPASSWORD");
                const token: string = await createResetToken(entity.key, "USERwithPASSWORD", "resetPasswordTokens");

                const info: UserInfo = await getUserPasswordService(entity).resetPassword({
                    secretKey: entity.key,
                    username: "USERwithPASSWORD",
                    token: token,
                    newPassword: "newPassword",
                });

                expect(info).contain({
                    username: "USERwithPASSWORD",
                    status: "normal",
                });
            });

            it("Fails to reset password if user on suspended", async () => {
                const token: string = await createResetToken(entity.key, "USERwithPASSWORD", "resetPasswordTokens");
                await entityUserService.suspend("USERwithPASSWORD");
                await getUserPasswordService(entity).resetPassword({
                    secretKey: entity.key,
                    username: "USERwithPASSWORD",
                    token: token,
                    newPassword: "newPassword",
                }).should.eventually.rejectedWith(Errors.ParentSuspendedError);
            });

            it("Fails to reset password if data are missing", async () => {
                await getUserPasswordService(entity).resetPassword({
                    secretKey: null,
                    username: null,
                    token: null,
                    newPassword: null,
                }).should.eventually.rejectedWith(Errors.ValidationError);
            });
        });

        describe("Change email", () => {
            let entity;
            let entityUserService: UserService;
            let emailTestData = {} as any;
            let emailStub: SinonStub;

            before(async () => {
                entity = await EntityService.findOne({ key: complexStructure.masterKey });
                entityUserService = getUserService(entity);
                await (new EntitySettingsService(entity)).patch({
                    emailTemplates: {
                        passwordRecovery: {
                            from: "Test <<EMAIL>>",
                            subject: "Test Password Reset",
                            html: "<a href=\"{{resetLink}}\">{{resetLink}}</a>",
                        },
                        changeEmail: {
                            from: "Skywind <<EMAIL>>",
                            subject: "Skywind Change Email Confirmation",
                            html: "<a href=\"{{resetLink}}\">{{resetLink}}</a>",
                        },
                    }
                });
                await entityUserService.create({
                    username: "USERwithEMAIL",
                    password: "password",
                    email: "<EMAIL>",
                });
                emailStub = stub(getEmailService(), "sendEmail").callsFake(([email], data, options) => {
                    emailTestData = {
                        email,
                        options,
                        data
                    };
                });
            });

            after(() => {
                emailStub.restore();
            });

            it("Requests email change", async () => {
                const domain = "https://bo.domain.com";
                const user: UserInfo = await requestChangeEmail(entity, "USERwithEMAIL", domain);

                expect(user).contain({ username: "USERwithEMAIL" });

                expect(emailTestData.email).to.be.equal("<EMAIL>");
                expect(emailTestData.options).to.exist;
                expect(emailTestData.options.domain).to.be.equal(domain);
            });

            it("Change Email", async () => {
                const domain = "http://bo.domain.com";
                const user: UserInfo = await requestChangeEmail(entity, "USERwithEMAIL", domain);

                expect(user).contain({ username: "USERwithEMAIL" });

                expect(emailTestData.email).to.be.equal("<EMAIL>");
                expect(emailTestData.options).to.exist;
                expect(emailTestData.options.token).to.exist;
                expect(emailTestData.options.domain).to.be.equal(domain);

                await changeEmail(entity, "USERwithEMAIL", {
                    token: emailTestData.options.token,
                    newEmail: "<EMAIL>",
                    domain
                });

                expect(emailTestData.email).to.equal("<EMAIL>");
                expect(emailTestData.options).to.exist;
                expect(emailTestData.options.token).to.exist;
                expect(emailTestData.options.domain).to.be.equal(domain);
            });

            it("Confirm email change", async () => {
                const domain = "http://bo.domain.com";
                const user: UserInfo = await requestChangeEmail(entity, "USERwithEMAIL", domain);

                expect(user).contain({ username: "USERwithEMAIL" });

                expect(emailTestData.email).to.be.equal("<EMAIL>");
                expect(emailTestData.options).to.exist;
                expect(emailTestData.options.token).to.exist;
                expect(emailTestData.options.domain).to.equal(domain);

                await changeEmail(entity, "USERwithEMAIL", {
                    token: emailTestData.options.token,
                    newEmail: "<EMAIL>",
                    domain
                });

                expect(emailTestData.email).to.equal("<EMAIL>");
                expect(emailTestData.options).to.exist;
                expect(emailTestData.options.token).to.exist;
                expect(emailTestData.options.domain).to.be.equal(domain);

                await confirmEmail(entity, "USERwithEMAIL", emailTestData.options.token);

                const userData = await entityUserService.findOne("USERwithEMAIL");

                expect(userData.email).to.be.equal(emailTestData.email);
            });

            it("Fails to request change email if entity is suspended", async () => {
                const tle1 = await EntityService.findOne({ key: complexStructure.tle1.key });
                const service = getUserService(tle1);
                await service.create({
                    username: "pawn",
                    password: "password",
                    email: "<EMAIL>",
                });
                await EntityService.suspend(entity, { name: "TLE1" });
                const frozenEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
                await requestChangeEmail(frozenEntity, "pawn", "https://bo.domain.com")
                    .should.eventually.rejectedWith(Errors.ParentSuspendedError);
            });

            it("Fails to request change email if user is suspended", async () => {
                await entityUserService.suspend("USERwithEMAIL");
                await requestChangeEmail(entity, "USERwithEMAIL", "https://bo.domain.com")
                    .should.eventually.rejectedWith(Errors.ParentSuspendedError);
            });

            describe("Force change email", () => {
                before(async () => {
                    entity = await EntityService.findOne({ key: complexStructure.tle2.key });
                    entityUserService = getUserService(entity);
                    await entityUserService.create({
                        username: "USERwithEMAIL",
                        password: "password",
                        email: "<EMAIL>",
                    });
                });

                it("Change email", async () => {
                    await forceChangeEmail(entity, "USERwithEMAIL", { email: "<EMAIL>" });

                    const userDataNew = await entityUserService.findOne("USERwithEMAIL");
                    expect(userDataNew.email).to.be.equal("<EMAIL>");
                });

                it("Email already exist - negative", async () => {
                    await entityUserService.create({
                        username: "another-user",
                        password: "password",
                        email: "<EMAIL>",
                    });

                    await forceChangeEmail(entity, "USERwithEMAIL", { email: "<EMAIL>" })
                        .should.eventually.rejectedWith(Errors.EmailAlreadyExistError);
                });

                it("Change email - entity is suspended - negative", async () => {
                    const tle2ent1 = await EntityService.findOne({ key: complexStructure.tle2ent1.key });
                    const service = getUserService(tle2ent1);
                    await service.create({
                        username: "pawn",
                        password: "password",
                        email: "<EMAIL>",
                    });
                    await EntityService.suspend(entity, { name: "ENT1" });
                    const frozenEntity = await EntityService.findOne({ key: complexStructure.tle2ent1.key });
                    await forceChangeEmail(frozenEntity, "pawn", { email: "<EMAIL>" })
                        .should.eventually.rejectedWith(Errors.ParentSuspendedError);
                });

                it("Change email - user is suspended - negative", async () => {
                    await entityUserService.suspend("USERwithEMAIL");
                    await forceChangeEmail(entity, "USERwithEMAIL", { email: "<EMAIL>" })
                        .should.eventually.rejectedWith(Errors.ParentSuspendedError);
                });
            });
        });

        describe("User authentication at first time", () => {

            let entity;
            let userService;

            before(async () => {

                entity = await EntityService.findOne({ key: complexStructure.masterKey });
                userService = getUserService(entity);

                config.userPasswordChangeCheck.isEnabled = true;

                await (new EntitySettingsService(entity)).patch({
                    isPasswordChangeEnabled: true,
                });
            });

            it("Login with 'change password' entity setting on - full scenario", async () => {

                await userService.create({
                    username: "Josefina-Izabel",
                    password: "password",
                    email: "<EMAIL>",
                });
                const error = await getUserAuthService(entity).login(
                    { username: "Josefina-Izabel", password: "password" })
                    .should.eventually.rejectedWith(Errors.ChangePasswordError);

                const token = error.changePasswordToken;

                const info: UserInfo = await getUserPasswordService(entity).resetPassword({
                    secretKey: entity.key,
                    username: "Josefina-Izabel",
                    token: token,
                    newPassword: "newPassword",
                });

                expect(info).contain({
                    username: "Josefina-Izabel",
                    status: "normal",
                });

                const loginInfo: LoginInfo = await getUserAuthService(entity).login(
                    { username: "Josefina-Izabel", password: "newPassword" });
                const expected = {
                    "username": "Josefina-Izabel",
                };
                expect(loginInfo)
                    .contain(expected)
                    .to.have.any.keys(["accessToken"]);
            });

            after(async () => {
                config.userPasswordChangeCheck.isEnabled = false;

                await (new EntitySettingsService(entity)).patch({
                    isPasswordChangeEnabled: false,
                });
            });
        });

        describe("User force change password", () => {

            let entity;
            let userService;

            before(async () => {

                entity = await EntityService.findOne({ key: complexStructure.masterKey });
                userService = getUserService(entity);

                config.userPasswordForceChangeCheck.isEnabled = true;

                await (new EntitySettingsService(entity)).patch({
                    isPasswordForceChangeEnabled: true,
                });
            });

            it("Login with 'change password' entity setting on and bo user that has force password entry", async () => {

                const createdAt = new Date();
                const dateOffset = TIME_CONSTS.MONTH_TICKS * 2;

                createdAt.setTime(createdAt.getTime() - dateOffset);

                await userService.create({
                    username: "Gregor",
                    password: "password",
                    email: "<EMAIL>",
                    forcePasswordChangePeriod: 1,
                    forcePasswordChangePeriodType: "monthly",
                    userType: "bo"
                });

                await Models.UserModel.update(
                    { createdAt: createdAt } as any,
                    { where: { email: "<EMAIL>" } }
                );

                const error = await getUserAuthService(entity).login(
                    { username: "Gregor", password: "password" })
                    .should.eventually.rejectedWith(Errors.ChangePasswordError);

                const token = error.changePasswordToken;

                const info: UserInfo = await getUserPasswordService(entity).resetPassword({
                    secretKey: entity.key,
                    username: "Gregor",
                    token: token,
                    newPassword: "newPassword",
                });

                expect(info).contain({
                    username: "Gregor",
                    status: "normal",
                });

                const loginInfo: LoginInfo = await getUserAuthService(entity).login(
                    { username: "Gregor", password: "newPassword" });
                const expected = {
                    "username": "Gregor",
                };
                expect(loginInfo)
                    .contain(expected)
                    .to.have.any.keys(["accessToken"]);
            });

            it("Patch user's force change password data and try to login", async () => {

                // check that login is fine now
                let loginInfo: LoginInfo = await getUserAuthService(entity).login(
                    { username: "Gregor", password: "newPassword" });
                const expected = {
                    "username": "Gregor",
                };
                expect(loginInfo)
                    .contain(expected)
                    .to.have.any.keys(["accessToken"]);

                // change password change period to 3 mins
                await userService.update("Gregor", {
                    forcePasswordChangePeriod: 3,
                    forcePasswordChangePeriodType: "minutely"
                });

                // set last password change date to 5 mins ago
                const updatedAt = new Date();
                const dateOffset = TIME_CONSTS.MINUTE_TICKS * 5;

                updatedAt.setTime(updatedAt.getTime() - dateOffset);

                await Models.UserModel.update(
                    { passwordChangedAt: updatedAt } as any,
                    { where: { email: "<EMAIL>" } }
                );

                const error = await getUserAuthService(entity).login(
                    { username: "Gregor", password: "newPassword" })
                    .should.eventually.rejectedWith(Errors.ChangePasswordError);

                const token = error.changePasswordToken;

                const info: UserInfo = await getUserPasswordService(entity).resetPassword({
                    secretKey: entity.key,
                    username: "Gregor",
                    token: token,
                    newPassword: "newPassword2",
                });

                expect(info).contain({
                    username: "Gregor",
                    status: "normal",
                });

                loginInfo = await getUserAuthService(entity).login(
                    { username: "Gregor", password: "newPassword2" });

                expect(loginInfo)
                    .contain(expected)
                    .to.have.any.keys(["accessToken"]);
            });

            after(async () => {
                config.userPasswordForceChangeCheck.isEnabled = false;

                await (new EntitySettingsService(entity)).patch({
                    isPasswordForceChangeEnabled: false,
                });
            });
        });

        describe("Two factor auth", () => {

            let otherEntity: BaseEntity;
            let otherUserService: UserService;
            let keyEntity: BaseEntity;
            let keyUserService: UserService;

            let emailUser: User;
            let smsUser: User;
            let googleUser: User;
            let regularUser: User;
            let emailStub;
            let smsStub;
            let entitySettings;

            before(async () => {
                const key = randomUUID();
                keyEntity = await EntityService.findOne({ key: complexStructure.masterKey });
                keyUserService = getUserService(keyEntity);

                emailStub = stub(EmailUtils, "sendAuthCodeEmail").resolves();
                smsStub = stub(SmsUtils, "sendAuthCodeSms").resolves();

                const data: CreateData = {
                    name: "two-auth-entity",
                    description: "Used for two auth test",
                    key: key,
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    title: "Empty title",
                    languages: ["en", "zh"],
                    jurisdictionCode: "COM"
                };

                const factory = getEntityFactory(keyEntity);

                otherEntity = await factory.createEntity(data);
                otherUserService = getUserService(otherEntity);

                entitySettings = {
                    twoFactorAuthSettings: {
                        isAuthEnabled: true,
                        authOptions: ["sms", "email", "google"],
                        smsTemplates: { by: "sms template" },
                        mailTemplates: { by: "mail template" }
                    }
                };
                await (new EntitySettingsService(otherEntity)).patch(entitySettings);

                const emailUserData = {
                    username: "email-user",
                    password: "pass2018!",
                    email: "<EMAIL>"
                };

                let userInfo = await otherUserService.create(emailUserData);
                emailUser = await otherUserService.findOne(userInfo.username) as User;

                const userData = {
                    username: "sms-user",
                    password: "pass2018!",
                    email: "<EMAIL>"
                };
                userInfo = await otherUserService.create(userData);
                smsUser = await otherUserService.findOne(userInfo.username) as User;

                const googleUserData = {
                    username: "google-user",
                    password: "pass2018!",
                    email: "<EMAIL>"
                };
                userInfo = await otherUserService.create(googleUserData);
                googleUser = await otherUserService.findOne(userInfo.username) as User;

                const regularUserData = {
                    username: "some-master-user",
                    password: "password",
                    email: "<EMAIL>",
                };
                userInfo = await keyUserService.create(regularUserData);
                regularUser = await keyUserService.findOne(userInfo.username) as User;

                config.twoFA.isEnabled = true;
            });

            after(() => {
                emailStub.restore();
                smsStub.restore();
            });

            it("Attempt to set email auth type", async () => {
                const twoFATokenData = { username: emailUser.username, userId: emailUser.id, entityId: otherEntity.id };
                try {
                    await getUserAuthService(otherEntity).login(
                        { username: emailUser.username, password: "pass2018!" });
                } catch (err) {
                    expect(err).to.be.instanceOf(Errors.TwoFATypeNotSetError);
                    expect(err.twoFAToken).to.exist;
                    expect(err.twoFATypes).to.exist;
                    expect(err.twoFATypes.length).to.eq(3);
                }

                await getUserAuthService(otherEntity).makeChallengeForSecondStepAuth(
                    twoFATokenData, "email", undefined);

                const generatedCode =
                    await unstashValueForToken(
                        `sw-management-api:secondStepAuth:${otherEntity.key}:${emailUser.username}`);

                expect(generatedCode).to.exist;

                const loginInfo: LoginInfo = await getUserAuthService(otherEntity).setSecondStepAuthType(
                    twoFATokenData, generatedCode, "email");

                const expected = {
                    "username": emailUser.username,
                };
                expect(loginInfo)
                    .contain(expected)
                    .to.have.any.keys(["accessToken"]);

                emailUser = await otherUserService.findOne(emailUser.username) as User;
                expect(emailUser.authInfo.defaultAuthType).to.eq("email");
                expect(emailUser.authInfo.authTypes[0]).to.eq("email");
            });

            it("Uses email template from entity settings.", async () => {
                const twoFATokenData = { username: emailUser.username, userId: emailUser.id, entityId: otherEntity.id };

                emailStub.reset();

                await getUserAuthService(otherEntity).makeChallengeForSecondStepAuth(
                    twoFATokenData, TWO_FA_TYPE.EMAIL, undefined, "by");

                const generatedCode =
                    await unstashValueForToken(
                        `sw-management-api:secondStepAuth:${otherEntity.key}:${emailUser.username}`);

                expect(generatedCode).to.exist;

                expect(emailUser.defaultTwoFAType).to.eq(TWO_FA_TYPE.EMAIL);
                expect(emailStub.calledOnce).to.eq(true);
                expect(emailStub.calledWith(emailUser.email, emailUser.username, generatedCode,
                    entitySettings.twoFactorAuthSettings.mailTemplates.by)).to.eq(true);
            });

            it("Login with auth type that is not present in entity settings", async () => {
                const editedSettings = {
                    twoFactorAuthSettings: {
                        isAuthEnabled: true,
                        authOptions: ["sms", "google"],
                        smsTemplates: { by: "sms template" },
                        mailTemplates: { by: "mail template" } as any
                    }
                };

                await (new EntitySettingsService(otherEntity)).patch(editedSettings);

                try {
                    await getUserAuthService(otherEntity).login(
                        { username: emailUser.username, password: "pass2018!" });
                } catch (err) {
                    expect(err).to.be.instanceOf(Errors.TwoFATypeNotSetError);
                    expect(err.twoFAToken).to.exist;
                    expect(err.twoFATypes).to.exist;
                    expect(err.twoFATypes.length).to.eq(2);
                }
            });

            it("Fail to set sms auth type", async () => {
                const twoFATokenData = { username: smsUser.username, userId: smsUser.id, entityId: otherEntity.id };
                // try to set without challenge
                await getUserAuthService(otherEntity).setSecondStepAuthType(
                    twoFATokenData, "AAAAAA", "sms", "+37529 1234567")
                    .should.be.eventually.rejectedWith(Errors.TwoFAAuthCodeExpired);

                await getUserAuthService(otherEntity).makeChallengeForSecondStepAuth(
                    twoFATokenData, "sms", "+37529 1234567");

                // try to set with incorrect code
                await getUserAuthService(otherEntity).setSecondStepAuthType(
                    twoFATokenData, "AAAAAA", "sms", "+37529 1234567")
                    .should.be.eventually.rejectedWith(Errors.AuthCodeIsIncorrect);
            });

            it("Attempt to set sms auth type. Should set user's phone field.", async () => {
                const twoFATokenData = { username: smsUser.username, userId: smsUser.id, entityId: otherEntity.id };
                await getUserAuthService(otherEntity).makeChallengeForSecondStepAuth(
                    twoFATokenData, "sms", "+37529 1234567");

                const generatedCode =
                    await unstashValueForToken(
                        `sw-management-api:secondStepAuth:${otherEntity.key}:${smsUser.username}`);

                expect(generatedCode).to.exist;

                const loginInfo: LoginInfo = await getUserAuthService(otherEntity).setSecondStepAuthType(
                    twoFATokenData, generatedCode, "sms", "+37529 1234567");

                const expected = {
                    "username": smsUser.username,
                };
                expect(loginInfo)
                    .contain(expected)
                    .to.have.any.keys(["accessToken"]);

                smsUser = await otherUserService.findOne(smsUser.username) as User;
                expect(smsUser.defaultTwoFAType).to.eq("sms");
                expect(smsUser.phone).to.eq("+37529 1234567");
            });

            it("Attempt to set sms auth type. Should use user's phone. Should not reset user's phone.", async () => {
                await getUserAuthService(otherEntity).resetUserAuth(smsUser.username, "sms");
                await otherUserService.update(smsUser.username, { phone: "+37529 1234567" });
                const twoFATokenData = { username: smsUser.username, userId: smsUser.id, entityId: otherEntity.id };

                await getUserAuthService(otherEntity).makeChallengeForSecondStepAuth(
                    twoFATokenData, "sms", "+37529 11111111");

                const generatedCode =
                    await unstashValueForToken(
                        `sw-management-api:secondStepAuth:${otherEntity.key}:${smsUser.username}`);

                expect(generatedCode).to.exist;

                const loginInfo: LoginInfo = await getUserAuthService(otherEntity).setSecondStepAuthType(
                    twoFATokenData, generatedCode, "sms", "+37529 11111111");

                const expected = {
                    "username": smsUser.username,
                };
                expect(loginInfo)
                    .contain(expected)
                    .to.have.any.keys(["accessToken"]);

                smsUser = await otherUserService.findOne(smsUser.username) as User;
                expect(smsUser.defaultTwoFAType).to.eq("sms");
                expect(smsUser.phone).to.eq("+37529 1234567");
            });

            it("Uses sms template from entity settings.", async () => {
                const twoFATokenData = { username: smsUser.username, userId: smsUser.id, entityId: otherEntity.id };

                smsStub.reset();

                await getUserAuthService(otherEntity).makeChallengeForSecondStepAuth(
                    twoFATokenData, "sms", "+37529 11111111", "by");

                const generatedCode = await unstashValueForToken(
                    `sw-management-api:secondStepAuth:${otherEntity.key}:${smsUser.username}`);

                expect(generatedCode).to.exist;

                expect(smsUser.defaultTwoFAType).to.eq("sms");
                expect(smsStub.calledOnce).to.eq(true);
                expect(smsStub.calledWith(smsUser.phone, generatedCode, smsUser.username,
                    entitySettings.twoFactorAuthSettings.smsTemplates.by)).to.eq(true);
            });

            it("Consequent login is rejected so that auth code is not sent too often", async () => {
                await getUserAuthService(otherEntity).login(
                    { username: smsUser.username, password: "pass2018!" });

                await getUserAuthService(otherEntity).login(
                    { username: smsUser.username, password: "pass2018!" })
                    .should.eventually.rejectedWith(Errors.TwoFAAuthCodeHasBeenSentRecently);
            });

            it("Fail to set google auth type due to expiration", async () => {
                const twoFATokenData = {
                    username: googleUser.username,
                    userId: googleUser.id,
                    entityId: otherEntity.id
                };
                // try to set without challenge
                await getUserAuthService(otherEntity).setSecondStepAuthType(
                    twoFATokenData, "AAAAAA", "google", undefined)
                    .should.be.eventually.rejectedWith(Errors.TwoFAAuthCodeExpired);

                await getUserAuthService(otherEntity).makeQRCodeForGoogleAuthSelection(twoFATokenData);

                // try to set with incorrect code
                await getUserAuthService(otherEntity).setSecondStepAuthType(
                    twoFATokenData, "AAAAAA", "google", undefined)
                    .should.be.eventually.rejectedWith(Errors.AuthCodeIsIncorrect);
            });

            it("Attempt to set google auth type", async () => {
                const twoFATokenData = {
                    username: googleUser.username,
                    userId: googleUser.id,
                    entityId: otherEntity.id
                };
                const resp = await getUserAuthService(otherEntity).makeQRCodeForGoogleAuthSelection(twoFATokenData);
                expect(resp.gaSecretKey).to.exist;

                const generatedSecret = await unstashValueForToken(
                    `sw-management-api:gaSecretAuth:${otherEntity.key}:${googleUser.username}`);

                expect(generatedSecret).to.exist;
                const formattedToken = authenticator.generateToken(generatedSecret);

                const loginInfo: LoginInfo = await getUserAuthService(otherEntity).setSecondStepAuthType(
                    twoFATokenData, formattedToken, "google", generatedSecret);

                const expected = {
                    "username": googleUser.username,
                };
                expect(loginInfo)
                    .contain(expected)
                    .to.have.any.keys(["accessToken"]);

                const googleUserRef = await otherUserService.findOne(googleUser.username) as User;
                expect(googleUserRef.defaultTwoFAType).to.eq("google");
                expect(googleUserRef.authInfo).to.deep.eq({
                    authTypes: [
                        "google"
                    ],
                    defaultAuthType: "google",
                    gaSecret: generatedSecret
                });
            });

            it("Fail to set auth type that is not present in entity settings", async () => {
                const twoFATokenData = {
                    username: googleUser.username,
                    userId: googleUser.id,
                    entityId: otherEntity.id
                };

                await getUserAuthService(otherEntity).setSecondStepAuthType(
                    twoFATokenData, "AAAA", "fake", undefined)
                    .should.be.eventually.rejectedWith(Errors.AuthTypeIsNotAllowed);

                await getUserAuthService(otherEntity).makeChallengeForSecondStepAuth(
                    twoFATokenData, "bad type", "+37529 1234567")
                    .should.be.eventually.rejectedWith(Errors.AuthTypeIsNotAllowed);
            });

            it("Rejects login for non 2FA user", async () => {
                const twoFATokenData = {
                    username: regularUser.username,
                    userId: regularUser.id,
                    entityId: keyEntity.id
                };

                await getUserAuthService(keyEntity).secondStepLogin(twoFATokenData, "123 456", "sms")
                    .should.be.eventually.rejectedWith(Errors.TwoFANotConfiguredForEntity);
            });

            it("Reset auth type", async () => {
                await getUserAuthService(otherEntity).resetUserAuth(googleUser.username, "google");
                await getUserAuthService(otherEntity).resetUserAuth(googleUser.username, "google")
                    .should.be.eventually.rejectedWith(Errors.TwoFATypeNotSetError);
                const googleUserRef = await otherUserService.findOne(googleUser.username) as User;
                expect(googleUserRef.defaultTwoFAType).to.eq(undefined);
            });

            it("Add google, sms and email auth types", async () => {
                const editedSettings = {
                    twoFactorAuthSettings: {
                        isAuthEnabled: true,
                        authOptions: ["sms", "google", "email"],
                        smsTemplates: { by: "sms template" },
                        mailTemplates: { by: "mail template" } as any
                    }
                };

                await (new EntitySettingsService(otherEntity)).patch(editedSettings);

                const twoFATokenData = {
                    username: googleUser.username,
                    userId: googleUser.id,
                    entityId: otherEntity.id
                };
                const resp = await getUserAuthService(otherEntity).makeQRCodeForGoogleAuthSelection(twoFATokenData);
                expect(resp.gaSecretKey).to.exist;

                const generatedSecret = await unstashValueForToken(
                    `sw-management-api:gaSecretAuth:${otherEntity.key}:${googleUser.username}`);

                expect(generatedSecret).to.exist;
                const formattedToken = authenticator.generateToken(generatedSecret);

                await getUserAuthService(otherEntity).addSecondStepAuthType(
                    twoFATokenData, formattedToken, "google", generatedSecret, false);

                await getUserAuthService(otherEntity).makeChallengeForSecondStepAuth(
                    twoFATokenData, "sms", "+37529 1234567");

                let generatedCode = await unstashValueForToken(
                    `sw-management-api:secondStepAuth:${otherEntity.key}:${googleUser.username}`);

                await getUserAuthService(otherEntity).addSecondStepAuthType(
                    twoFATokenData, generatedCode, "sms", "+37529 1234567");

                let googleUserRef = await otherUserService.findOne(googleUser.username) as User;
                expect(googleUserRef.authInfo).to.deep.eq({
                    authTypes: [
                        "google", "sms"
                    ],
                    gaSecret: generatedSecret
                });

                emailStub.reset();

                await getUserAuthService(otherEntity).makeChallengeForSecondStepAuth(
                    twoFATokenData, TWO_FA_TYPE.EMAIL, undefined, "by");

                generatedCode = await unstashValueForToken(
                    `sw-management-api:secondStepAuth:${otherEntity.key}:${googleUser.username}`);

                await getUserAuthService(otherEntity).addSecondStepAuthType(twoFATokenData, generatedCode, "email");

                googleUserRef = await otherUserService.findOne(googleUser.username) as User;
                expect(googleUserRef.authInfo).to.deep.eq({
                    authTypes: [
                        "google", "sms", "email"
                    ],
                    gaSecret: generatedSecret
                });
            });

            it("Attempt to login for user without default authType set", async () => {
                const loginResult = (await getUserAuthService(otherEntity).login(
                    { username: googleUser.username, password: "pass2018!" })) as any;

                expect(loginResult.defaultAuthType).to.be.undefined;
                expect(loginResult.userAuthTypes).to.deep.eq([
                    "google", "sms", "email"
                ]);
            });

            it("Set default auth type", async () => {
                await getUserAuthService(otherEntity).setUserAuthAsDefault(googleUser.username, "n/a")
                    .should.be.eventually.rejectedWith(Errors.TwoFATypeNotSetError);

                await getUserAuthService(otherEntity).setUserAuthAsDefault(googleUser.username, "sms");

                const googleUserRef = await otherUserService.findOne(googleUser.username) as User;
                expect(googleUserRef.authInfo.defaultAuthType).to.eq("sms");
            });

            it("Attempt to login for user when default authType set and disabled", async () => {
                entitySettings = {
                    twoFactorAuthSettings: {
                        isAuthEnabled: true,
                        authOptions: ["email", "google"],
                        smsTemplates: { by: "sms template" },
                        mailTemplates: { by: "mail template" }
                    }
                };
                await (new EntitySettingsService(otherEntity)).patch(entitySettings);

                await getUserAuthService(otherEntity).setUserAuthAsDefault(googleUser.username, "sms");

                const loginResult = (await getUserAuthService(otherEntity).login(
                    { username: googleUser.username, password: "pass2018!" })) as any;

                expect(loginResult.defaultAuthType).to.be.undefined;
                expect(loginResult.userAuthTypes).to.deep.eq([
                    "google", "email"
                ]);
            });

            function unstashValueForToken(token: string): Promise<string> {
                return redis.usingDb<string>(async (db) => {
                    return db.get(token);
                });
            }
        });
    });

    describe("Search Users", () => {
        let masterUserService: UserService;
        let tle1UserService: UserService;
        let ent1UserService: UserService;
        let ent2UserService: UserService;

        before(async () => {
            await truncate();
            await createComplexStructure();
            const entity = await EntityService.findOne({ key: complexStructure.masterKey });
            masterUserService = getUserService(entity);
            await masterUserService.create({
                username: "MASTER",
                password: "password",
                email: "<EMAIL>",
            });

            const tle1 = await EntityService.findOne({ key: complexStructure.tle1.key });
            tle1UserService = getUserService(tle1);

            const ent1 = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            ent1UserService = getUserService(ent1);

            const ent2 = await EntityService.findOne({ key: complexStructure.tle1ent2.key });
            ent2UserService = getUserService(ent2);
        });

        it.skip("List all users from keyEntity", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.masterKey });

            await tle1UserService.create({
                username: "TLE1-User",
                password: "password",
                email: "<EMAIL>",
            });

            await ent1UserService.create({
                username: "ENT1-User",
                password: "password",
                email: "<EMAIL>",
            });

            const expected = [
                {
                    "status": "normal",
                    "username": "ENT1-User",
                    "email": "<EMAIL>",
                    "entity": "TLE1:ENT1:",
                    "grantedPermissions": [],
                    "firstName": null,
                    "lastName": null,
                },
                {
                    "status": "normal",
                    "username": "MASTER",
                    "email": "<EMAIL>",
                    "entity": "",
                    "grantedPermissions": [],
                    "firstName": null,
                    "lastName": null,
                },
                {
                    "status": "normal",
                    "username": "SUPERADMIN",
                    "email": "<EMAIL>",
                    "entity": "",
                    "grantedPermissions": [
                        "id:encode",
                        "id:decode",
                        "agent",
                        "agent:view",
                        "audit",
                        "country",
                        "country:add",
                        "country:remove",
                        "currency",
                        "currency:add",
                        "currency:remove",
                        "entity",
                        "entity:balance",
                        "entity:change-state",
                        "entity:create",
                        "entity:delete",
                        "entity:edit",
                        "entity:game",
                        "entity:game:change-state",
                        "entity:game:history",
                        "entity:game:url",
                        "entity:game:view",
                        "entity:view",
                        "finance",
                        "finance:credit",
                        "finance:debit",
                        "jurisdiction",
                        "jurisdiction:create",
                        "jurisdiction:delete",
                        "jurisdiction:edit",
                        "jurisdiction:view",
                        "keyentity:agent",
                        "keyentity:agent:view",
                        "keyentity:audit",
                        "keyentity:game",
                        "keyentity:game:change-state",
                        "keyentity:game:history",
                        "keyentity:game:url",
                        "keyentity:game:view",
                        "keyentity:gamecategory",
                        "keyentity:gamecategory:create",
                        "keyentity:gamecategory:delete",
                        "keyentity:gamecategory:edit",
                        "keyentity:gamecategory:view",
                        "keyentity:gamegroup",
                        "keyentity:gamegroup:create",
                        "keyentity:gamegroup:edit",
                        "keyentity:gamegroup:view",
                        "keyentity:gamelabel",
                        "keyentity:gamelabel:view",
                        "keyentity:gameprovider",
                        "keyentity:gameprovider:change-secret",
                        "keyentity:gameprovider:change-state",
                        "keyentity:gameprovider:create",
                        "keyentity:gameprovider:game",
                        "keyentity:gameprovider:game:create",
                        "keyentity:gameprovider:game:delete",
                        "keyentity:gameprovider:game:edit",
                        "keyentity:gameprovider:game:view",
                        "keyentity:gameprovider:view",
                        "keyentity:integration",
                        "keyentity:integration:create",
                        "keyentity:integration:delete",
                        "keyentity:integration:edit",
                        "keyentity:integration:view",
                        "keyentity:lobby",
                        "keyentity:lobby:create",
                        "keyentity:lobby:delete",
                        "keyentity:lobby:edit",
                        "keyentity:lobby:view",
                        "keyentity:merchant",
                        "keyentity:merchant:create",
                        "keyentity:merchant:edit",
                        "keyentity:merchant:view",
                        "keyentity:notifications",
                        "keyentity:notifications:view",
                        "keyentity:payment",
                        "keyentity:payment:add-key",
                        "keyentity:payment:create",
                        "keyentity:payment:edit",
                        "keyentity:payment:execute",
                        "keyentity:payment:get-key",
                        "keyentity:payment:transfer-in",
                        "keyentity:payment:transfer-out",
                        "keyentity:payment:view",
                        "keyentity:permissions",
                        "keyentity:permissions:view",
                        "keyentity:player",
                        "keyentity:player:change-state",
                        "keyentity:player:create",
                        "keyentity:player:deposit",
                        "keyentity:player:edit",
                        "keyentity:player:login",
                        "keyentity:player:promotion",
                        "keyentity:player:view",
                        "keyentity:player:withdrawal",
                        "keyentity:promotion",
                        "keyentity:promotion:create",
                        "keyentity:promotion:delete",
                        "keyentity:promotion:edit",
                        "keyentity:promotion:view",
                        "keyentity:report",
                        "keyentity:report:currency",
                        "keyentity:report:games",
                        "keyentity:report:ggr",
                        "keyentity:report:players",
                        "keyentity:report:wallet-currency",
                        "keyentity:royalties",
                        "keyentity:royalties:create",
                        "keyentity:royalties:delete",
                        "keyentity:royalties:edit",
                        "keyentity:royalties:view",
                        "keyentity:site",
                        "keyentity:terminal",
                        "keyentity:terminal:create",
                        "keyentity:terminal:delete",
                        "keyentity:terminal:edit",
                        "keyentity:terminal:token",
                        "keyentity:terminal:view",
                        "keyentity:user",
                        "keyentity:user:change-password",
                        "keyentity:user:change-state",
                        "keyentity:user:create",
                        "keyentity:user:edit",
                        "keyentity:user:view",
                        "language",
                        "language:add",
                        "language:remove",
                        "merchant",
                        "merchant:create",
                        "merchant:edit",
                        "merchant:view",
                        "payment",
                        "payment:add-key",
                        "payment:create",
                        "payment:edit",
                        "payment:execute",
                        "payment:get-key",
                        "payment:transfer-in",
                        "payment:transfer-out",
                        "payment:view",
                        "permissions",
                        "permissions:view",
                        "player",
                        "player:change-state",
                        "player:create",
                        "player:edit",
                        "player:login",
                        "player:promotion",
                        "player:view",
                        "report",
                        "report-without-limit",
                        "report:currency",
                        "report:games",
                        "report:ggr",
                        "report:players",
                        "report:wallet-currency",
                        "role",
                        "royalties",
                        "royalties:create",
                        "royalties:delete",
                        "royalties:edit",
                        "royalties:view",
                        "settings",
                        "site",
                        "user",
                        "user:change-password",
                        "user:change-state",
                        "user:create",
                        "user:edit",
                        "user:view",
                    ],
                    "firstName": null,
                    "lastName": null,
                },
                {
                    "status": "normal",
                    "username": "TLE1-User",
                    "email": "<EMAIL>",
                    "entity": "TLE1:",
                    "grantedPermissions": [],
                    "firstName": null,
                    "lastName": null,
                },
            ];

            let userList: UserInfo[];
            try {
                userList = await search(entity, entity, {});
            } catch (err) {
                return Promise.reject("Should not get here");
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(4);

            userList.map((item: any) => delete item.lastLogin);
            expect(userList).to.shallowDeepEqual(expected);
        });

        it("List all users from some of sub-entity and check their entity path", async () => {
            await masterUserService.create({
                username: "user-for-find001",
                password: "password",
                email: "<EMAIL>",
            });

            const tle1 = await EntityService.findOne({ key: complexStructure.tle1.key });
            await tle1UserService.create({
                username: "user-for-find002",
                password: "password",
                email: "<EMAIL>",
            });

            await ent1UserService.create({
                username: "user-for-find003",
                password: "password",
                email: "<EMAIL>",
            });

            const masterEntity = await EntityService.findOne({ key: complexStructure.masterKey });

            const expected = [
                {
                    "status": "normal",
                    "username": "user-for-find002",
                    "email": "<EMAIL>",
                    "entity": "",
                    "grantedPermissions": [],
                    "firstName": null,
                    "lastName": null,
                },
                {
                    "status": "normal",
                    "username": "user-for-find003",
                    "email": "<EMAIL>",
                    "entity": "ENT1:",
                    "grantedPermissions": [],
                    "firstName": null,
                    "lastName": null,
                },
            ];

            let userList: UserInfo[];
            try {
                userList = await search(masterEntity, tle1, { username: { [Op.iLike]: "user-for-find%" } });

                userList.map((item: any) => delete item.lastLogin);
                expect(userList).to.shallowDeepEqual(expected);
            } catch (err) {
                return Promise.reject(err);
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(2);
            expect(userList).to.shallowDeepEqual(expected);
        });

        it("List all users from keyEntity with mask username filter", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.masterKey });

            await masterUserService.create({
                username: "USER-MASTER",
                password: "password",
                email: "<EMAIL>",
            });

            await tle1UserService.create({
                username: "Cassandra",
                password: "password",
                email: "<EMAIL>",
            });

            await ent1UserService.create({
                username: "Castalia",
                password: "password",
                email: "<EMAIL>",
            });

            const expected = [
                {
                    "username": "Cassandra",
                    "status": "normal",
                    "email": "<EMAIL>",
                    "entity": "TLE1:",
                    "grantedPermissions": [],
                    "firstName": null,
                    "lastName": null,
                },
                {
                    "username": "Castalia",
                    "status": "normal",
                    "email": "<EMAIL>",
                    "entity": "TLE1:ENT1:",
                    "grantedPermissions": [],
                    "firstName": null,
                    "lastName": null,
                },
            ];

            let userList: UserInfo[];
            try {
                userList = await search(entity, entity, { username: { [Op.iLike]: "Cas%" } });
            } catch (err) {
                return Promise.reject("Should not get here");
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(2);

            userList.map((item: any) => delete item.lastLogin);
            expect(userList).to.shallowDeepEqual(expected);
        });

        it("List all users from keyEntity with exact username filter", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.masterKey });

            await tle1UserService.create({
                username: "Amirgos",
                password: "password",
                email: "<EMAIL>",
            });

            await ent1UserService.create({
                username: "Amirgossian",
                password: "password",
                email: "<EMAIL>",
            });

            const expected = [
                {
                    "username": "Amirgos",
                    "status": "normal",
                    "email": "<EMAIL>",
                    "entity": "TLE1:",
                    "grantedPermissions": [],
                    "firstName": null,
                    "lastName": null,
                },
            ];

            let userList: UserInfo[];
            try {
                userList = await search(entity, entity, { username: "Amirgos" });
            } catch (err) {
                return Promise.reject("Should not get here");
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(1);

            userList.map((item: any) => delete item.lastLogin);
            expect(userList).to.shallowDeepEqual(expected);
        });

        it("List all users from keyEntity with exact email filter", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.masterKey });

            await tle1UserService.create({
                username: "Maahes-TLE1",
                password: "password",
                email: "<EMAIL>",
            });

            await ent1UserService.create({
                username: "Mandulis-ENT1",
                password: "password",
                email: "<EMAIL>",
            });

            const expected = [
                {
                    "username": "Maahes-TLE1",
                    "status": "normal",
                    "email": "<EMAIL>",
                    "entity": "TLE1:",
                    "grantedPermissions": [],
                    "firstName": null,
                    "lastName": null,
                },
            ];

            let userList: UserInfo[];
            try {
                userList = await search(entity, entity, { email: { [Op.iLike]: "<EMAIL>" } });
            } catch (err) {
                return Promise.reject("Should not get here");
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(1);

            userList.map((item: any) => delete item.lastLogin);
            expect(userList).to.shallowDeepEqual(expected);
        });

        it("List all users from keyEntity with filter of username and email", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.masterKey });

            await tle1UserService.create({
                username: "Svarog",
                password: "password",
                email: "<EMAIL>",
            });

            await ent1UserService.create({
                username: "Jarilo",
                password: "password",
                email: "<EMAIL>",
            });

            const expected = [
                {
                    "username": "Svarog",
                    "status": "normal",
                    "email": "<EMAIL>",
                    "entity": "TLE1:",
                    "grantedPermissions": [],
                    "firstName": null,
                    "lastName": null,
                },
            ];

            let userList: UserInfo[];
            try {
                userList = await search(entity, entity,
                    {
                        email: "<EMAIL>",
                        username: { [Op.iLike]: "Svar%" },
                    }
                );
            } catch (err) {
                return Promise.reject("Should not get here");
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(1);

            userList.map((item: any) => delete item.lastLogin);
            expect(userList).to.shallowDeepEqual(expected);
        });

        it("List all users from keyEntity with filter of entity name", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.masterKey });

            const tle1ent2 = await EntityService.findOne({ key: complexStructure.tle1ent2.key });
            const ent2UserService = getUserService(tle1ent2);

            await ent2UserService.create({
                username: "Bellerophon",
                password: "password",
                email: "<EMAIL>",
            });

            await ent1UserService.create({
                username: "Ornytion",
                password: "password",
                email: "<EMAIL>",
            });

            const expected = [
                {
                    "username": "Bellerophon",
                    "status": "normal",
                    "email": "<EMAIL>",
                    "entity": "TLE1:ENT2:",
                    "grantedPermissions": [],
                    "firstName": null,
                    "lastName": null,
                },
            ];

            let userList: UserInfo[];
            try {
                userList = await search(entity, entity, { entity: "ENT2" });
            } catch (err) {
                return Promise.reject("Should not get here");
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(1);

            userList.map((item: any) => delete item.lastLogin);
            expect(userList).to.shallowDeepEqual(expected);
        });

        it("List all users from keyEntity sorting by full name", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.masterKey });
            const tle1ent2 = await EntityService.findOne({ key: complexStructure.tle1ent2.key });
            const ent2UserService = getUserService(tle1ent2);
            const usersToCreate = [
                {
                    firstName: "bbb",
                    lastName: "aaa",
                    username: "sortinguser1",
                    password: "password",
                    email: "<EMAIL>",
                }, {
                    firstName: "aaa",
                    lastName: "bbb",
                    username: "sortinguser0",
                    password: "password",
                    email: "<EMAIL>",
                }, {
                    firstName: "ccc",
                    lastName: "0aaa",
                    username: "sortinguser2",
                    password: "password",
                    email: "<EMAIL>",
                }
            ];
            for (const user of usersToCreate) {
                await ent2UserService.create(user);
            }
            let userList: UserInfo[];
            try {
                userList = await search(entity, entity, { sortBy: "fullName", ["email"]: { [Op.iLike]: "%sort%" } });
            } catch (err) {
                return Promise.reject("Should not get here");
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(3);

            userList.map((item: any) => delete item.lastLogin);
            const sortedUsers = usersToCreate
                .sort((userA, userB) => {
                    const fullNameA = `${userA.firstName} ${userA.lastName}`;
                    const fullNameB = `${userB.firstName} ${userB.lastName}`;
                    if (fullNameA < fullNameB) {
                        return -1;
                    } else if (fullNameA > fullNameB) {
                        return 1;
                    } else {
                        return 0;
                    }
                }).map(user => {
                    delete user.password;
                    return user;
                });

            expect(userList).to.shallowDeepEqual(sortedUsers);
        });

        it("Under entity with no users", async () => {
            await tle1UserService.create({
                username: "SPY-BASE",
                password: "password",
                email: "<EMAIL>",
            });

            await ent1UserService.create({
                username: "SPY-SUBENTITY",
                password: "password",
                email: "<EMAIL>",
            });

            const tle2ent3 = await EntityService.findOne({ key: complexStructure.tle2ent3.key });

            let userList: UserInfo[];
            try {
                userList = await search(tle2ent3, tle2ent3, {});
            } catch (err) {
                return Promise.reject("Should not get here");
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(0);
        });

        it("With filtering of non-existing email", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.masterKey });

            await tle1UserService.create({
                username: "Menhit-USER-TLE1",
                password: "password",
                email: "<EMAIL>",
            });

            await ent1UserService.create({
                username: "Sekhmet-USER-ENT1",
                password: "password",
                email: "<EMAIL>",
            });

            let userList: UserInfo[];
            try {
                userList = await search(entity, entity, { email: "<EMAIL>" });
            } catch (err) {
                return Promise.reject("Should not get here");
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(0);
        });

        it("Search by Custom data", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.masterKey });

            await tle1UserService.create({
                username: "Menhit-USER-TLE11",
                password: "password",
                email: "<EMAIL>",
                customData: {
                    role: null,
                    nickname: "123",
                    cardNumber: "555"
                }
            });

            await ent1UserService.create({
                username: "Sekhmet-USER-ENT11",
                password: "password",
                email: "<EMAIL>",
                customData: {
                    role: { id: "topLevel", title: "Top Level" },
                    nickname: "nikita"
                }
            });

            let userList: UserInfo[];
            try {
                userList = await search(entity,
                    entity,
                    {},
                    JSON.stringify({ role: { id: "topLevel", title: "Top Level" } }));
            } catch (err) {
                return Promise.reject("Should not get here");
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(1);
            expect(userList[0].username).to.be.equal("Sekhmet-USER-ENT11");

            try {
                userList = await search(entity,
                    entity,
                    {},
                    JSON.stringify({ role: { id: "topLevel", title: "Top Level" }, nickname: "nikita" }));
            } catch (err) {
                return Promise.reject("Should not get here");
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(1);
            expect(userList[0].username).to.be.equal("Sekhmet-USER-ENT11");

            try {
                userList = await search(entity,
                    entity,
                    {},
                    JSON.stringify({ role: null, cardNumber: "555" }));
            } catch (err) {
                return Promise.reject("Should not get here");
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(1);
            expect(userList[0].username).to.be.equal("Menhit-USER-TLE11");

        });

        it("Search by Custom data roleId", async () => {
            const entity = await EntityService.findOne({ key: complexStructure.tle1ent2.key });
            await ent2UserService.create({
                username: "Sekhmet-USER-ENT21",
                password: "password",
                email: "<EMAIL>",
                customData: {
                    role: { id: "entityLevel1", title: "Entity Level 1" },
                    nickname: "nikita1"
                }
            });

            await ent2UserService.create({
                username: "Sekhmet-USER-ENT22",
                password: "password",
                email: "<EMAIL>",
                customData: {
                    role: { id: "entityLevel2", title: "Entity Level 2" },
                    nickname: "nikita2"
                }
            });

            let userList: UserInfo[];
            try {
                userList = await search(entity,
                    entity,
                    { customDataRoleId: { [Op.in]: ["entityLevel1"] } }
                );
            } catch (err) {
                return Promise.reject("Should not get here");
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(1);
            expect(userList[0].username).to.be.equal("Sekhmet-USER-ENT21");

            try {
                userList = await search(entity,
                    entity,
                    { customDataRoleId: { [Op.notIn]: ["entityLevel1"] } }
                );
            } catch (err) {
                return Promise.reject("Should not get here");
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(1);
            expect(userList[0].username).to.be.equal("Sekhmet-USER-ENT22");

            try {
                userList = await search(entity,
                    entity,
                    { customDataRoleId: { [Op.in]: ["entityLevel1", "entityLevel2"] } }
                );
            } catch (err) {
                return Promise.reject("Should not get here");
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(2);

            try {
                userList = await search(entity,
                    entity,
                    { customDataRoleId: { [Op.notIn]: ["entityLevel1", "entityLevel2"] } }
                );
            } catch (err) {
                return Promise.reject("Should not get here");
            }

            expect(userList).to.be.an("array");
            expect(userList).to.have.lengthOf(0);
        });

        it("Get user by non-existing username - negative", async () => {
            await ent1UserService.findOne("NOT_EXISTING_USER_NAME")
                .should.eventually.rejectedWith(Errors.UserNotExist);
        });

        it("Suspend and restore non-existing user - negative", async () => {
            await ent1UserService.suspend("NOT_EXISTING_USER_NAME")
                .should.eventually.rejectedWith(Errors.UserNotExist);
            await ent1UserService.restore("NOT_EXISTING_USER_NAME")
                .should.eventually.rejectedWith(Errors.UserNotExist);
        });

        describe("Search Users with roles", async () => {

            let entity;

            before(async () => {

                const data: CreateData = {
                    name: "BrandChebureckTest",
                    description: "Used for two auth test",
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    title: "Empty title",
                    languages: ["en", "zh"],
                    jurisdictionCode: "COM"
                };

                const master = await EntityService.findOne({ key: complexStructure.masterKey });
                entity = await getEntityFactory(master).createEntity(data);

                const permissions = await PermissionService.getPermissionsDescriptions();

                const [role1, role2, role3] = await getRoleModel().bulkCreate([
                    {
                        entityId: entity.id,
                        title: "Role1",
                        permissions: permissions.slice(0, 1).map(x => x.code).sort(),
                        isShared: false
                    },
                    {
                        entityId: entity.id,
                        title: "Role2",
                        permissions: permissions.slice(1, 2).map(x => x.code).sort(),
                        isShared: false
                    },
                    {
                        entityId: entity.id,
                        title: "Role3",
                        permissions: permissions.slice(2, 3).map(x => x.code).sort(),
                        isShared: false
                    }
                ], { returning: true });

                const userService = getUserService(entity);
                await userService.create({
                    username: "Chebureck1",
                    password: "password",
                    email: "<EMAIL>",
                    roles: [
                        new RoleImpl(role1).toEncodedInfo(),
                        new RoleImpl(role2).toEncodedInfo(),
                        new RoleImpl(role3).toEncodedInfo()
                    ]
                });

                await userService.create({
                    username: "Chebureck2",
                    password: "password",
                    email: "<EMAIL>",
                    roles: [new RoleImpl(role1).toEncodedInfo()]
                });

                await userService.create({
                    username: "Chebureck3",
                    password: "password",
                    email: "<EMAIL>",
                    roles: [new RoleImpl(role3).toEncodedInfo()]
                });
            });

            it("Search users with many roles", async () => {
                const users: DetailedUserInfo[] = await search(entity, entity, {
                    limit: 2,
                    status: "normal",
                });

                expect(users.length).to.be.equal(2);
                expect(PagingHelper.hasPagingInfo(users)).to.be.true;

                const pagingInfo = PagingHelper.getPagingInfo(users);
                expect(pagingInfo).to.be.deep.equal({
                    total: 3,
                    limit: 2,
                    offset: 0
                });
            });

            it("Search users by roleId", async () => {
                const users: DetailedUserInfo[] = await search(entity, entity, {
                    limit: 3,
                    status: "normal",
                    roleId: { [Op.in]: [1, 2] }
                });

                expect(users.length).to.be.equal(2);
                expect(PagingHelper.hasPagingInfo(users)).to.be.true;

                const pagingInfo = PagingHelper.getPagingInfo(users);
                expect(pagingInfo).to.be.deep.equal({
                    total: 2,
                    limit: 3,
                    offset: 0
                });
            });

            it("Search users by excluded roleId", async () => {
                const users: DetailedUserInfo[] = await search(entity, entity, {
                    limit: 3,
                    status: "normal",
                    roleId: { [Op.notIn]: [2, 4] }
                });

                expect(users.length).to.be.equal(1);
                expect(PagingHelper.hasPagingInfo(users)).to.be.true;

                const pagingInfo = PagingHelper.getPagingInfo(users);
                expect(pagingInfo).to.be.deep.equal({
                    total: 1,
                    limit: 3,
                    offset: 0
                });
            });
        });
    });
});
