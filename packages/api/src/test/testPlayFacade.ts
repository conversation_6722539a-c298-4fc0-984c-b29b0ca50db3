import {
    AnonymousPlayFacadeImpl,
    DefaultPlayServiceFactory,
    DefaultSessionService,
    DisabledExtBetWinHistoryService,
    ExtBetWinHistoryService,
    PlayFacadeImpl
} from "@skywind-group/sw-management-gameprovider-core";
import { WalletFacade } from "@skywind-group/sw-management-wallet";
import { createPlayerGameSession } from "@skywind-group/sw-management-playersession";
import { getRedisPool } from "../skywind/storage/redis";
import { defaultGameSessionFactory } from "../skywind/services/gameauth/sessionFactory";
import { defaultStartGameService } from "../skywind/services/gameauth/defaultStartGameService";
import { defaultOperatorDetailsRepository } from "../skywind/services/gameauth/defaultOperatorInfoRepository";
import config from "../skywind/config";
import {
    ProviderAuthDetails,
    SettingsRequest,
    SettingsWithoutToken,
    SettingsWithoutTokenService
} from "@skywind-group/sw-management-gameprovider";
import { defaultSettingsWithoutTokenService } from "../skywind/services/gameauth/defaultSettingsWithoutTokenService";
import { defaultDeferredPaymentFacade } from "../skywind/services/deferredPayments";
import { createBetWinHistoryService } from "@skywind-group/sw-management-gameprovider-core";
import { sequelize } from "../skywind/storage/db";
import { createConductor } from "@skywind-group/sw-wallet";
import { getConfig } from "../skywind/wallet";

const testSettingsWithoutTokenService: SettingsWithoutTokenService = {
    async auth(auth: ProviderAuthDetails): Promise<void> {
        return undefined;
    },
    async getSettings(auth: ProviderAuthDetails, req: SettingsRequest): Promise<SettingsWithoutToken> {
        return { operatorInfo: {} } as any;
    }
};

export const testAnonymousPlayFacade = new AnonymousPlayFacadeImpl(WalletFacade,
    new DefaultPlayServiceFactory(),
    defaultSettingsWithoutTokenService.get(),
    new DisabledExtBetWinHistoryService());

export const testAnonymousPlayFacadeWithMockedSettingsService = new AnonymousPlayFacadeImpl(WalletFacade,
    new DefaultPlayServiceFactory(),
    testSettingsWithoutTokenService,
    new DisabledExtBetWinHistoryService());

const testsFakeInternalAPIService = {
    addPromoToPlayer: () => {/**/
    },
    getPlayerPromotions: () => ([])
} as any;
export const testPlayFacade = new PlayFacadeImpl(
    WalletFacade,
    new DefaultPlayServiceFactory(),
    new DefaultSessionService(
        createPlayerGameSession(getRedisPool(), config.startGameToken.expiresIn),
        defaultGameSessionFactory.get()),
    undefined,
    defaultStartGameService.get(),
    defaultOperatorDetailsRepository.get(),
    undefined,
    defaultDeferredPaymentFacade.get(),
    new ExtBetWinHistoryService(config.extGameproviderHistory.awaitHistorySave,
        new DefaultSessionService(
            createPlayerGameSession(getRedisPool(), config.startGameToken.expiresIn),
            defaultGameSessionFactory.get())),
    createBetWinHistoryService(sequelize),
    createConductor(getConfig("direct")),
    config.gameToken,
    testsFakeInternalAPIService);
