import {
    AnonymousPlayFacade,
    AnonymousPlayFacadeImpl,
    CachedOperatorInfoStorage,
    DefaultPlayServiceFactory,
    DefaultSessionService,
    DisabledExtBetWinHistoryService,
    ExtBetWinHistoryService,
    GameAuthGateway,
    IExtBetWinHistoryService,
    NatsOperatorInfoNotification,
    OperatorInfoCacheNotification,
    OperatorInfoStorage,
    PlayFacade,
    PlayFacadeImpl,
    PlayServiceFactory,
    RedisOperatorInfoCacheNotification,
    RedisOperatorInfoNotification,
    RemoteGameSessionFactory,
    RemoteOperatorInfoRepository,
    RemoteSettingsWithoutTokenService,
    RemoteStartGameService
} from "@skywind-group/sw-management-gameprovider-core";
import { lazy, Lazy } from "@skywind-group/sw-utils";
import { WalletFacade } from "@skywind-group/sw-management-wallet";
import {
    createDeferredPaymentFacade,
    DeferredPaymentFacade,
    DeferredPaymentNotificationProcessor
} from "@skywind-group/sw-management-deferredpayment";
import { createPlayerGameSession } from "@skywind-group/sw-management-playersession";
import { getRedisFactory, getRedisPool } from "./storage/redis";
import {
    GameSessionFactory,
    GameSessionService,
    OperatorInfoNotification,
    OperatorInfoRepository,
    SettingsWithoutTokenService,
    StartGameService
} from "@skywind-group/sw-management-gameprovider";
import config from "./config";
import {
    createSubscriptionFactory,
    DeferredPaymentSubscriptionFactory
} from "@skywind-group/sw-deferred-payment-cache";
import { createMessaging, Messaging } from "@skywind-group/sw-messaging";
import {
    BetWinHistoryService,
    createBetWinHistoryService
} from "@skywind-group/sw-management-gameprovider-core";
import { RegulationSessionStorage } from "@skywind-group/sw-adapter-regulation-support";
import { sequelize } from "./storage/db";
import { createConductor, IOperationConductor } from "@skywind-group/sw-wallet";
import { getConfig } from "./wallet";

// todo aguzanov consider to change to DI approach

export const defaultGameAuthGateway: Lazy<GameAuthGateway> =
    lazy(() => new GameAuthGateway(config.gameAuthAPI.url, config.gameAuthAPI.keepAlive));

export const remoteGameSessionFactory: Lazy<GameSessionFactory<any>> =
    lazy<GameSessionFactory<any>>(() =>
        new RemoteGameSessionFactory(defaultGameAuthGateway.get(), config.internalServerToken));

export const remoteSettingsWithoutTokenService: Lazy<SettingsWithoutTokenService> =
    lazy<SettingsWithoutTokenService>(() =>
        new RemoteSettingsWithoutTokenService(defaultGameAuthGateway.get(), config.internalServerToken));

export const remoteStartGameService: Lazy<StartGameService> =
    lazy<StartGameService>(() => new RemoteStartGameService(defaultGameAuthGateway.get()));

export const remoteOperatorInfoRepository: Lazy<OperatorInfoRepository> =
    lazy<OperatorInfoRepository>(() => new RemoteOperatorInfoRepository(defaultGameAuthGateway.get(),
        config.internalServerToken));

export const defaultExtBetWinHistoryService: Lazy<IExtBetWinHistoryService> = lazy(() =>
    config.extGameproviderHistory.on ?
    new ExtBetWinHistoryService(config.extGameproviderHistory.awaitHistorySave, defaultGameSessionService.get()) :
    new DisabledExtBetWinHistoryService()
);

export const defaultBetWinHistoryService: Lazy<BetWinHistoryService> = lazy(() =>
    createBetWinHistoryService(sequelize, config.disableSaveCanceledRound)
);

export const defaultWalletOperationConductor: Lazy<IOperationConductor> = lazy(() =>
    createConductor(getConfig(config.walletConductor.type))
)

export const defaultDeferredPaymentFacade: Lazy<DeferredPaymentFacade> = lazy(() =>
    createDeferredPaymentFacade({
        on: config.deferredPayment.on,
        url: config.deferredPayment.url,
        secret: config.internalServerToken.secret,
        cacheExpireIn: config.startGameToken.expiresIn,
        keepAlive: config.deferredPayment.keepAlive,
        retries: config.deferredPayment.retries,
        requestTimeout: config.deferredPayment.requestTimeout
    }, getRedisPool())
);

export const defaultGameSessionService: Lazy<GameSessionService> = lazy(() =>
    new DefaultSessionService(
        createPlayerGameSession(getRedisPool(), config.startGameToken.expiresIn),
        remoteGameSessionFactory.get()));

export const defaultRcSessionStorage: Lazy<RegulationSessionStorage<unknown>> = lazy<RegulationSessionStorage<unknown>>(() =>
    new RegulationSessionStorage<unknown>("rc")
);

export const defaultOperatorInfoCacheNotification: Lazy<OperatorInfoCacheNotification> =
    lazy(() => new RedisOperatorInfoCacheNotification(getRedisFactory(),
        config.operatorInfoRepository.cacheChannelPrefix));

export const cachedOperatorInfoStorage: Lazy<OperatorInfoStorage> =
    lazy(() => new CachedOperatorInfoStorage(remoteOperatorInfoRepository.get(),
        defaultOperatorInfoCacheNotification.get(),
        config.operatorInfoRepository.cacheTTL, config.operatorInfoRepository.checkInterval));

export const messaging: Lazy<Messaging> = lazy(() => createMessaging(config.nats));

export const deferredPaymentSubscriptionFactory: Lazy<DeferredPaymentSubscriptionFactory> = lazy(() => {
    const type = getNotificationType();
    if (type === "redis") {
        return createSubscriptionFactory({
            type: "redis",
            factory: getRedisFactory()
        });
    } else if (type === "nats") {
        return createSubscriptionFactory({ type: "nats", messaging: messaging.get() });
    }
});

export const deferredPaymentNotificationProcessor: Lazy<DeferredPaymentNotificationProcessor> = lazy(() =>
    new DeferredPaymentNotificationProcessor(createPlayerGameSession(getRedisPool(), config.startGameToken.expiresIn),
        defaultDeferredPaymentFacade.get(),
        deferredPaymentSubscriptionFactory.get()));

export const defaultPlayServiceFactory: Lazy<PlayServiceFactory> = lazy(() => new DefaultPlayServiceFactory());

export const defaultOperatorInfoNotification: Lazy<OperatorInfoNotification> = lazy(() => {
    const type = getNotificationType();
    if (type === "redis") {
        return new RedisOperatorInfoNotification(
            getRedisFactory(),
            config.operatorInfoRepository.channelPrefix);
    } else if (type === "nats") {
        return new NatsOperatorInfoNotification(messaging.get(), config.operatorInfoRepository.channelPrefix);
    }
});

function getNotificationType() {
    if (!["redis", "nats"].includes(config.notificationType)) {
        throw new Error(`Unknown deferred payment subscription type ${config.notificationType}`);
    }
    return config.notificationType;
}

export const defaultPlayFacade: Lazy<PlayFacade> = lazy(() => new PlayFacadeImpl(
    WalletFacade,
    defaultPlayServiceFactory.get(),
    defaultGameSessionService.get(),
    defaultRcSessionStorage.get(),
    remoteStartGameService.get(),
    cachedOperatorInfoStorage.get(),
    cachedOperatorInfoStorage.get(),
    defaultDeferredPaymentFacade.get(),
    defaultExtBetWinHistoryService.get(),
    defaultBetWinHistoryService.get(),
    defaultWalletOperationConductor.get(),
    config.gameToken));

export const defaultAnonymousPlayFacade: Lazy<AnonymousPlayFacade> =
    lazy(() => new AnonymousPlayFacadeImpl(WalletFacade,
        defaultPlayServiceFactory.get(),
        remoteSettingsWithoutTokenService.get(),
        defaultExtBetWinHistoryService.get()));
